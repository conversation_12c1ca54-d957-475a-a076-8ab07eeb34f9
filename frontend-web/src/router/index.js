/**
 * Smart Factory WMS - Vue Router Configuration
 *
 * This file contains all route definitions and navigation guards
 * for the Smart Factory WMS frontend application.
 */

import { createRouter, createWebHistory } from "vue-router";

// Simple home component
const Home = {
  template:
    "<div><h2>Welcome to Smart Factory WMS</h2><p>Quasar notifications are now working!</p></div>",
};

// All components are now inline or will be created as needed

const routes = [
  // Home route
  {
    path: "/",
    name: "Home",
    component: Home,
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

export default router;
