/**
 * Vue I18n Plugin Configuration
 *
 * This file configures Vue I18n for internationalization support
 * in the Smart Factory WMS frontend application.
 */

import { createI18n } from "vue-i18n";

// Import locale messages
const messages = {
  en: {
    app: {
      name: "Smart Factory WMS",
      subtitle: "Warehouse Management System",
    },
    auth: {
      title: "Smart Factory WMS",
      welcome_message: "Welcome to Smart Factory WMS",
      username_label: "Username or Email",
      password_label: "Password",
      remember_me: "Remember me",
      sign_in_button: "Sign In",
      forgot_password: "Forgot Password?",
      language_label: "Language",
      loading_message: "Signing in...",
      login_success: "Successfully logged in",
      login_failed: "Login failed",
      logout_success: "Successfully logged out",
      logout_error: "Error occurred during logout",
      error_invalid_credentials: "Invalid username or password",
      error_account_locked: "Account is locked. Please contact administrator.",
      error_network: "Network error. Please try again.",
      error_server: "Server error. Please try again later.",
      forgot_password_success: "Password reset email sent",
      forgot_password_failed: "Failed to send password reset email",
      reset_password_success: "Password reset successfully",
      reset_password_failed: "Failed to reset password",
      change_password_success: "Password changed successfully",
      change_password_failed: "Failed to change password",
    },
    validation: {
      required: "This field is required",
      min_length: "Minimum {min} characters required",
      max_length: "Maximum {max} characters allowed",
      email_format: "Please enter a valid email address",
      username_format: "Only letters, numbers, dots and underscores allowed",
      password_match: "Passwords do not match",
    },
    common: {
      guest: "Guest",
      loading: "Loading...",
      error: "Error",
      success: "Success",
      cancel: "Cancel",
      confirm: "Confirm",
      save: "Save",
      close: "Close",
    },
  },
  ja: {
    app: {
      name: "スマートファクトリーWMS",
      subtitle: "倉庫管理システム",
    },
    auth: {
      title: "スマートファクトリーWMS",
      welcome_message: "スマートファクトリーWMSへようこそ",
      username_label: "ユーザー名またはメール",
      password_label: "パスワード",
      remember_me: "ログイン状態を保持",
      sign_in_button: "サインイン",
      forgot_password: "パスワードを忘れましたか？",
      language_label: "言語",
      loading_message: "サインイン中...",
      login_success: "ログインしました",
      login_failed: "ログインに失敗しました",
      logout_success: "ログアウトしました",
      logout_error: "ログアウト中にエラーが発生しました",
      error_invalid_credentials: "ユーザー名またはパスワードが無効です",
      error_account_locked:
        "アカウントがロックされています。管理者にお問い合わせください。",
      error_network: "ネットワークエラーです。再試行してください。",
      error_server: "サーバーエラーです。しばらくしてから再試行してください。",
      forgot_password_success: "パスワードリセットメールを送信しました",
      forgot_password_failed: "パスワードリセットメールの送信に失敗しました",
      reset_password_success: "パスワードをリセットしました",
      reset_password_failed: "パスワードのリセットに失敗しました",
      change_password_success: "パスワードを変更しました",
      change_password_failed: "パスワードの変更に失敗しました",
    },
    validation: {
      required: "この項目は必須です",
      min_length: "最低{min}文字必要です",
      max_length: "最大{max}文字まで入力可能です",
      email_format: "有効なメールアドレスを入力してください",
      username_format: "英数字、ドット、アンダースコアのみ使用可能です",
      password_match: "パスワードが一致しません",
    },
    common: {
      guest: "ゲスト",
      loading: "読み込み中...",
      error: "エラー",
      success: "成功",
      cancel: "キャンセル",
      confirm: "確認",
      save: "保存",
      close: "閉じる",
    },
  },
  zh: {
    app: {
      name: "智能工厂WMS",
      subtitle: "仓库管理系统",
    },
    auth: {
      title: "智能工厂WMS",
      welcome_message: "欢迎使用智能工厂WMS",
      username_label: "用户名或邮箱",
      password_label: "密码",
      remember_me: "记住我",
      sign_in_button: "登录",
      forgot_password: "忘记密码？",
      language_label: "语言",
      loading_message: "登录中...",
      login_success: "登录成功",
      login_failed: "登录失败",
      logout_success: "成功登出",
      logout_error: "登出时发生错误",
      error_invalid_credentials: "用户名或密码无效",
      error_account_locked: "账户已锁定，请联系管理员。",
      error_network: "网络错误，请重试。",
      error_server: "服务器错误，请稍后重试。",
      forgot_password_success: "密码重置邮件已发送",
      forgot_password_failed: "发送密码重置邮件失败",
      reset_password_success: "密码重置成功",
      reset_password_failed: "密码重置失败",
      change_password_success: "密码修改成功",
      change_password_failed: "密码修改失败",
    },
    validation: {
      required: "此字段为必填项",
      min_length: "最少需要{min}个字符",
      max_length: "最多允许{max}个字符",
      email_format: "请输入有效的邮箱地址",
      username_format: "只允许字母、数字、点和下划线",
      password_match: "密码不匹配",
    },
    common: {
      guest: "访客",
      loading: "加载中...",
      error: "错误",
      success: "成功",
      cancel: "取消",
      confirm: "确认",
      save: "保存",
      close: "关闭",
    },
  },
  vi: {
    app: {
      name: "Smart Factory WMS",
      subtitle: "Hệ thống quản lý kho",
    },
    auth: {
      title: "Smart Factory WMS",
      welcome_message: "Chào mừng đến với Smart Factory WMS",
      username_label: "Tên đăng nhập hoặc Email",
      password_label: "Mật khẩu",
      remember_me: "Ghi nhớ đăng nhập",
      sign_in_button: "Đăng nhập",
      forgot_password: "Quên mật khẩu?",
      language_label: "Ngôn ngữ",
      loading_message: "Đang đăng nhập...",
      login_success: "Đăng nhập thành công",
      login_failed: "Đăng nhập thất bại",
      logout_success: "Đăng xuất thành công",
      logout_error: "Có lỗi xảy ra khi đăng xuất",
      error_invalid_credentials: "Tên đăng nhập hoặc mật khẩu không hợp lệ",
      error_account_locked:
        "Tài khoản đã bị khóa. Vui lòng liên hệ quản trị viên.",
      error_network: "Lỗi mạng. Vui lòng thử lại.",
      error_server: "Lỗi máy chủ. Vui lòng thử lại sau.",
      forgot_password_success: "Email đặt lại mật khẩu đã được gửi",
      forgot_password_failed: "Gửi email đặt lại mật khẩu thất bại",
      reset_password_success: "Đặt lại mật khẩu thành công",
      reset_password_failed: "Đặt lại mật khẩu thất bại",
      change_password_success: "Đổi mật khẩu thành công",
      change_password_failed: "Đổi mật khẩu thất bại",
    },
    validation: {
      required: "Trường này là bắt buộc",
      min_length: "Tối thiểu {min} ký tự",
      max_length: "Tối đa {max} ký tự",
      email_format: "Vui lòng nhập địa chỉ email hợp lệ",
      username_format: "Chỉ cho phép chữ cái, số, dấu chấm và gạch dưới",
      password_match: "Mật khẩu không khớp",
    },
    common: {
      guest: "Khách",
      loading: "Đang tải...",
      error: "Lỗi",
      success: "Thành công",
      cancel: "Hủy",
      confirm: "Xác nhận",
      save: "Lưu",
      close: "Đóng",
    },
  },
};

// Create I18n instance
const i18n = createI18n({
  legacy: false,
  locale: "en",
  fallbackLocale: "en",
  messages,
  globalInjection: true,
});

export default i18n;
