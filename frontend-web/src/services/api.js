/**
 * Smart Factory WMS - API Service
 * 
 * This module provides a configured Axios instance for API communication
 * with request/response interceptors, error handling, and authentication.
 */

import axios from 'axios'
import store from '@/store'
import router from '@/router'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = store.state.auth.token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // Add request ID for tracing
    config.headers['X-Request-ID'] = generateRequestId()
    
    // Add timestamp
    config.headers['X-Request-Time'] = new Date().toISOString()
    
    // Log request in development
    if (import.meta.env.DEV) {
      console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {
        data: config.data,
        params: config.params
      })
    }
    
    return config
  },
  (error) => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // Log response in development
    if (import.meta.env.DEV) {
      console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data
      })
    }
    
    return response
  },
  async (error) => {
    const originalRequest = error.config
    
    // Log error in development
    if (import.meta.env.DEV) {
      console.error(`❌ ${originalRequest?.method?.toUpperCase()} ${originalRequest?.url}`, {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      })
    }
    
    // Handle 401 Unauthorized
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true
      
      try {
        // Try to refresh token
        await store.dispatch('auth/refreshToken')
        
        // Retry original request with new token
        const token = store.state.auth.token
        if (token) {
          originalRequest.headers.Authorization = `Bearer ${token}`
          return api(originalRequest)
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        store.dispatch('auth/logout')
        router.push('/login')
        return Promise.reject(refreshError)
      }
    }
    
    // Handle 403 Forbidden
    if (error.response?.status === 403) {
      router.push('/unauthorized')
    }
    
    // Handle 500 Server Error
    if (error.response?.status >= 500) {
      router.push('/server-error')
    }
    
    // Handle network errors
    if (!error.response) {
      console.error('Network error:', error.message)
      // Could show a network error notification here
    }
    
    return Promise.reject(error)
  }
)

// Utility functions
function generateRequestId() {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15)
}

// API helper methods
export const apiHelpers = {
  // Generic CRUD operations
  async get(url, params = {}) {
    const response = await api.get(url, { params })
    return response.data
  },
  
  async post(url, data = {}) {
    const response = await api.post(url, data)
    return response.data
  },
  
  async put(url, data = {}) {
    const response = await api.put(url, data)
    return response.data
  },
  
  async patch(url, data = {}) {
    const response = await api.patch(url, data)
    return response.data
  },
  
  async delete(url) {
    const response = await api.delete(url)
    return response.data
  },
  
  // File upload
  async upload(url, file, onProgress = null) {
    const formData = new FormData()
    formData.append('file', file)
    
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
    
    if (onProgress) {
      config.onUploadProgress = (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress(percentCompleted)
      }
    }
    
    const response = await api.post(url, formData, config)
    return response.data
  },
  
  // File download
  async download(url, filename = null) {
    const response = await api.get(url, {
      responseType: 'blob'
    })
    
    // Create download link
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
    
    return response.data
  },
  
  // Paginated requests
  async getPaginated(url, page = 1, limit = 20, params = {}) {
    const response = await api.get(url, {
      params: {
        page,
        limit,
        ...params
      }
    })
    return response.data
  },
  
  // Search requests
  async search(url, query, params = {}) {
    const response = await api.get(url, {
      params: {
        q: query,
        ...params
      }
    })
    return response.data
  }
}

// Specific API endpoints
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: () => api.post('/auth/logout'),
  refresh: (refreshToken) => api.post('/auth/refresh', { refresh_token: refreshToken }),
  me: () => api.get('/auth/me'),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (token, password) => api.post('/auth/reset-password', { token, password }),
  changePassword: (data) => api.post('/auth/change-password', data)
}

export const inventoryAPI = {
  getAll: (params) => apiHelpers.getPaginated('/inventory', params.page, params.limit, params),
  getById: (id) => apiHelpers.get(`/inventory/${id}`),
  create: (data) => apiHelpers.post('/inventory', data),
  update: (id, data) => apiHelpers.put(`/inventory/${id}`, data),
  delete: (id) => apiHelpers.delete(`/inventory/${id}`),
  adjust: (id, data) => apiHelpers.post(`/inventory/${id}/adjust`, data),
  search: (query) => apiHelpers.search('/inventory/search', query)
}

export const receivingAPI = {
  getAll: (params) => apiHelpers.getPaginated('/receiving', params.page, params.limit, params),
  getById: (id) => apiHelpers.get(`/receiving/${id}`),
  create: (data) => apiHelpers.post('/receiving', data),
  update: (id, data) => apiHelpers.put(`/receiving/${id}`, data),
  delete: (id) => apiHelpers.delete(`/receiving/${id}`),
  complete: (id) => apiHelpers.post(`/receiving/${id}/complete`),
  cancel: (id) => apiHelpers.post(`/receiving/${id}/cancel`)
}

export const shipmentAPI = {
  getAll: (params) => apiHelpers.getPaginated('/shipment', params.page, params.limit, params),
  getById: (id) => apiHelpers.get(`/shipment/${id}`),
  create: (data) => apiHelpers.post('/shipment', data),
  update: (id, data) => apiHelpers.put(`/shipment/${id}`, data),
  delete: (id) => apiHelpers.delete(`/shipment/${id}`),
  ship: (id) => apiHelpers.post(`/shipment/${id}/ship`),
  cancel: (id) => apiHelpers.post(`/shipment/${id}/cancel`)
}

export const productionAPI = {
  getAll: (params) => apiHelpers.getPaginated('/production', params.page, params.limit, params),
  getById: (id) => apiHelpers.get(`/production/${id}`),
  create: (data) => apiHelpers.post('/production', data),
  update: (id, data) => apiHelpers.put(`/production/${id}`, data),
  delete: (id) => apiHelpers.delete(`/production/${id}`),
  start: (id) => apiHelpers.post(`/production/${id}/start`),
  complete: (id) => apiHelpers.post(`/production/${id}/complete`),
  cancel: (id) => apiHelpers.post(`/production/${id}/cancel`)
}

export const usersAPI = {
  getAll: (params) => apiHelpers.getPaginated('/users', params.page, params.limit, params),
  getById: (id) => apiHelpers.get(`/users/${id}`),
  create: (data) => apiHelpers.post('/users', data),
  update: (id, data) => apiHelpers.put(`/users/${id}`, data),
  delete: (id) => apiHelpers.delete(`/users/${id}`),
  activate: (id) => apiHelpers.post(`/users/${id}/activate`),
  deactivate: (id) => apiHelpers.post(`/users/${id}/deactivate`)
}

export const reportsAPI = {
  inventory: (params) => apiHelpers.get('/reports/inventory', params),
  production: (params) => apiHelpers.get('/reports/production', params),
  receiving: (params) => apiHelpers.get('/reports/receiving', params),
  shipment: (params) => apiHelpers.get('/reports/shipment', params),
  export: (type, params) => apiHelpers.download(`/reports/${type}/export`, `${type}_report.xlsx`)
}

export default api
