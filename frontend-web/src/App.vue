<template>
    <div id="app">
        <header class="app-header">
            <h1>Smart Factory WMS</h1>
            <button @click="testNotification" class="test-btn">Test Notification</button>
        </header>

        <main class="main-content">
            <router-view />
        </main>

        <footer class="app-footer">
            <span>&copy; 2024 Smart Factory WMS</span>
        </footer>
    </div>
</template>

<script>
export default {
    name: 'App',
    methods: {
        testNotification() {
            this.$q.notify({
                type: 'positive',
                message: 'Quasar notification is working!',
                position: 'top'
            })
        }
    }
}
</script>

<style scoped>
.app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background-color: #1976d2;
    color: white;
}

.test-btn {
    background-color: #4caf50;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
}

.main-content {
    padding: 2rem;
    min-height: calc(100vh - 120px);
}

.app-footer {
    background-color: #f5f5f5;
    padding: 1rem;
    text-align: center;
    border-top: 1px solid #ddd;
}
</style>
