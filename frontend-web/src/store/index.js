/**
 * Smart Factory WMS - Vuex Store Configuration
 *
 * This file contains the main Vuex store configuration with all modules
 * for state management in the Smart Factory WMS frontend application.
 */

import { createStore } from "vuex";

// Import store modules
import auth from "./modules/auth";

// Create and configure store
const store = createStore({
  modules: {
    auth,
  },

  strict: import.meta.env.DEV,

  plugins: [
    // Persistence plugin for auth state
    (store) => {
      // Subscribe to mutations
      store.subscribe((mutation, state) => {
        // Save auth state to localStorage
        if (mutation.type.startsWith("auth/")) {
          localStorage.setItem(
            "auth",
            JSON.stringify({
              token: state.auth.token,
              refreshToken: state.auth.refreshToken,
              user: state.auth.user,
            })
          );
        }

        // Save UI preferences to localStorage
        if (
          mutation.type.startsWith("ui/SET_THEME") ||
          mutation.type.startsWith("ui/SET_LANGUAGE")
        ) {
          localStorage.setItem(
            "ui_preferences",
            JSON.stringify({
              theme: state.ui.theme,
              language: state.ui.language,
              sidebarCollapsed: state.ui.sidebarCollapsed,
            })
          );
        }
      });

      // Restore state from localStorage on initialization
      const savedAuth = localStorage.getItem("auth");
      if (savedAuth) {
        try {
          const authData = JSON.parse(savedAuth);
          if (authData.token) {
            store.commit("auth/SET_TOKEN", authData.token);
            store.commit("auth/SET_REFRESH_TOKEN", authData.refreshToken);
            store.commit("auth/SET_USER", authData.user);
          }
        } catch (error) {
          console.error("Failed to restore auth state:", error);
          localStorage.removeItem("auth");
        }
      }

      const savedUI = localStorage.getItem("ui_preferences");
      if (savedUI) {
        try {
          const uiData = JSON.parse(savedUI);
          if (uiData.theme) store.commit("ui/SET_THEME", uiData.theme);
          if (uiData.language) store.commit("ui/SET_LANGUAGE", uiData.language);
          if (typeof uiData.sidebarCollapsed === "boolean") {
            store.commit("ui/SET_SIDEBAR_COLLAPSED", uiData.sidebarCollapsed);
          }
        } catch (error) {
          console.error("Failed to restore UI preferences:", error);
          localStorage.removeItem("ui_preferences");
        }
      }
    },
  ],
});

// Development tools
if (import.meta.env.DEV) {
  window.store = store;
}

export default store;
