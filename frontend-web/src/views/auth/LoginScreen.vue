<template>
  <div
    class="login-container tw-min-h-screen tw-flex tw-items-center tw-justify-center tw-bg-gradient-to-br tw-from-blue-50 tw-to-indigo-100">
    <q-card class="login-card tw-w-full tw-max-w-md tw-mx-4 tw-shadow-2xl">
      <q-card-section class="tw-text-center tw-py-8">
        <!-- Company Logo -->
        <div class="tw-mb-6">
          <q-img src="/logo.svg" alt="Smart Factory WMS Logo" class="tw-w-16 tw-h-16 tw-mx-auto tw-mb-4" no-spinner
            @error="logoError = true">
            <template v-slot:error>
              <div
                class="tw-w-16 tw-h-16 tw-mx-auto tw-bg-primary tw-rounded-lg tw-flex tw-items-center tw-justify-center">
                <q-icon name="factory" size="32px" color="white" />
              </div>
            </template>
          </q-img>
          <h1 class="text-h4 text-primary tw-font-bold tw-mb-2">
            {{ $t('auth.title') }}
          </h1>
          <p class="text-subtitle1 text-grey-7">
            {{ $t('auth.welcome_message') }}
          </p>
        </div>

        <!-- Language Selector -->
        <div class="tw-mb-6">
          <q-select v-model="selectedLanguage" :options="languageOptions" :label="$t('auth.language_label')" outlined
            dense options-dense emit-value map-options class="tw-max-w-xs tw-mx-auto"
            @update:model-value="changeLanguage">
            <template v-slot:prepend>
              <q-icon name="language" />
            </template>
          </q-select>
        </div>
      </q-card-section>

      <q-card-section class="tw-px-8 tw-pb-8">
        <!-- Login Form -->
        <q-form @submit="handleLogin" class="tw-space-y-4">
          <!-- Username/Email Input -->
          <q-input v-model="loginForm.username" :label="$t('auth.username_label')" :error="!!formErrors.username"
            :error-message="formErrors.username" outlined dense autocomplete="username" :disable="isLoading"
            @blur="validateField('username')" @keyup.enter="handleLogin">
            <template v-slot:prepend>
              <q-icon name="person" />
            </template>
          </q-input>

          <!-- Password Input -->
          <q-input v-model="loginForm.password" :label="$t('auth.password_label')"
            :type="showPassword ? 'text' : 'password'" :error="!!formErrors.password"
            :error-message="formErrors.password" outlined dense autocomplete="current-password" :disable="isLoading"
            @blur="validateField('password')" @keyup.enter="handleLogin">
            <template v-slot:prepend>
              <q-icon name="lock" />
            </template>
            <template v-slot:append>
              <q-btn :icon="showPassword ? 'visibility_off' : 'visibility'" flat round dense
                @click="togglePasswordVisibility" :disable="isLoading"
                :aria-label="showPassword ? 'Hide password' : 'Show password'" />
            </template>
          </q-input>

          <!-- Remember Me Checkbox -->
          <div class="tw-flex tw-items-center tw-justify-between tw-mt-4">
            <q-checkbox v-model="loginForm.remember_me" :label="$t('auth.remember_me')" :disable="isLoading"
              class="text-grey-7" />

            <!-- Forgot Password Link -->
            <q-btn :label="$t('auth.forgot_password')" flat no-caps dense color="primary" :disable="isLoading"
              @click="showForgotPassword = true" class="tw-text-sm" />
          </div>

          <!-- Sign In Button -->
          <q-btn :label="isLoading ? $t('auth.loading_message') : $t('auth.sign_in_button')" type="submit"
            color="primary" unelevated size="lg" :loading="isLoading" :disable="isLoading" class="tw-w-full tw-mt-6"
            no-caps>
            <template v-slot:loading>
              <q-spinner-hourglass class="tw-mr-2" />
              {{ $t('auth.loading_message') }}
            </template>
          </q-btn>
        </q-form>
      </q-card-section>
    </q-card>

    <!-- Forgot Password Dialog -->
    <q-dialog v-model="showForgotPassword" persistent>
      <q-card class="tw-w-full tw-max-w-sm">
        <q-card-section>
          <div class="text-h6">{{ $t('auth.forgot_password') }}</div>
        </q-card-section>

        <q-card-section class="tw-pt-0">
          <q-form @submit="handleForgotPassword">
            <q-input v-model="forgotPasswordForm.email" :label="$t('auth.username_label')" :error="!!formErrors.email"
              :error-message="formErrors.email" outlined dense type="email" autocomplete="email" :disable="isLoading">
              <template v-slot:prepend>
                <q-icon name="email" />
              </template>
            </q-input>
          </q-form>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn :label="$t('common.cancel')" flat color="grey" @click="showForgotPassword = false"
            :disable="isLoading" />
          <q-btn :label="$t('common.confirm')" color="primary" @click="handleForgotPassword" :loading="isLoading"
            :disable="isLoading" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { useAuth } from '@/composables/useAuth'
import { onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()
const {
  loginForm,
  forgotPasswordForm,
  showPassword,
  formErrors,
  isLoading,
  login,
  forgotPassword,
  togglePasswordVisibility,
  clearErrors
} = useAuth()

// Local state
const logoError = ref(false)
const showForgotPassword = ref(false)
const selectedLanguage = ref(locale.value)

// Language options
const languageOptions = [
  { label: 'English', value: 'en', icon: '🇺🇸' },
  { label: '日本語', value: 'ja', icon: '🇯🇵' },
  { label: '中文', value: 'zh', icon: '🇨🇳' },
  { label: 'Tiếng Việt', value: 'vi', icon: '🇻🇳' }
]

// Methods
const changeLanguage = (newLocale) => {
  locale.value = newLocale
  localStorage.setItem('user_language', newLocale)
}

const validateField = (fieldName) => {
  // Clear previous errors for this field
  if (formErrors.value[fieldName]) {
    delete formErrors.value[fieldName]
  }
}

const handleLogin = async () => {
  clearErrors()
  const result = await login()

  if (!result.success && result.errors) {
    // Form validation errors are already handled by the composable
    console.log('Login validation errors:', result.errors)
  }
}

const handleForgotPassword = async () => {
  clearErrors()
  const result = await forgotPassword()

  if (result.success) {
    showForgotPassword.value = false
    forgotPasswordForm.email = ''
  }
}

// Initialize language from storage
onMounted(() => {
  const savedLanguage = localStorage.getItem('user_language')
  if (savedLanguage && ['en', 'ja', 'zh', 'vi'].includes(savedLanguage)) {
    selectedLanguage.value = savedLanguage
    locale.value = savedLanguage
  }
})
</script>

<style scoped>
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.login-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive design */
@media (max-width: 640px) {
  .login-card {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }
}

/* Accessibility improvements */
.login-card :focus {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

/* Animation for smooth transitions */
.login-card {
  transition: all 0.3s ease;
}

.login-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .login-container {
    background: #000;
  }

  .login-card {
    background: #fff;
    border: 2px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .login-card {
    transition: none;
  }

  .login-card:hover {
    transform: none;
  }
}
</style>
