{"name": "smart-factory-wms-frontend", "version": "1.0.0", "description": "Smart Factory WMS Frontend - Vue.js Application with Quasar Framework", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "cypress open", "test:e2e:headless": "cypress run", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --ignore-path .gitignore", "format": "prettier --write src/", "format:check": "prettier --check src/", "analyze": "vite-bundle-analyzer"}, "dependencies": {"@mdi/font": "^7.4.47", "@quasar/extras": "^1.16.9", "axios": "^1.6.2", "chart.js": "^4.4.0", "date-fns": "^2.30.0", "file-saver": "^2.0.5", "jsbarcode": "^3.11.5", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "qrcode": "^1.5.3", "quasar": "^2.14.2", "uuid": "^9.0.1", "vue": "^3.3.8", "vue-chartjs": "^5.2.0", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5", "xlsx": "^0.18.5"}, "devDependencies": {"@cypress/vue": "^6.0.0", "@quasar/vite-plugin": "^1.6.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/test-utils": "^2.4.2", "autoprefixer": "^10.4.16", "cypress": "^13.6.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "jsdom": "^23.0.1", "postcss": "^8.4.32", "prettier": "^3.1.0", "sass": "^1.69.5", "start-server-and-test": "^2.0.3", "tailwindcss": "^3.3.6", "vite": "^5.0.0", "vite-bundle-analyzer": "^0.7.0", "vitest": "^0.34.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}